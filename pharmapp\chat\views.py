from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.db.models import Q
from .models import ChatMessage
from .forms import ChatMessageForm

User = get_user_model()

@login_required
def chat_view(request, receiver_id=None):
    users = User.objects.exclude(id=request.user.id)
    selected_user = None
    messages = []

    if receiver_id:
        selected_user = get_object_or_404(User, id=receiver_id)
        # Messages between the logged-in user and the selected user
        messages = ChatMessage.objects.filter(
            (Q(sender=request.user) & Q(receiver=selected_user)) |
            (Q(sender=selected_user) & Q(receiver=request.user))
        ).order_by('timestamp')
        # Mark messages as read
        ChatMessage.objects.filter(sender=selected_user, receiver=request.user, is_read=False).update(is_read=True)

    if request.method == 'POST':
        form = ChatMessageForm(request.POST)
        if form.is_valid() and selected_user:
            chat_message = form.save(commit=False)
            chat_message.sender = request.user
            chat_message.receiver = selected_user
            chat_message.save()
            return redirect('chat:chat_view', receiver_id=selected_user.id)
    else:
        form = ChatMessageForm()
        if selected_user:
            form.fields['receiver'].initial = selected_user # Pre-select receiver if a chat is active
            form.fields['receiver'].widget = forms.HiddenInput() # Hide if a user is selected
        else:
            # Ensure 'receiver' field allows selection if no user is chosen yet
            form.fields['receiver'].queryset = User.objects.exclude(id=request.user.id)
            form.fields['receiver'].empty_label = "Select a user to chat with"


    context = {
        'users': users,
        'selected_user': selected_user,
        'messages': messages,
        'form': form,
    }
    return render(request, 'chat/chat_interface.html', context)

@login_required
def send_message_view(request):
    if request.method == 'POST':
        form = ChatMessageForm(request.POST)
        if form.is_valid():
            chat_message = form.save(commit=False)
            chat_message.sender = request.user
            # receiver is already set by the form
            chat_message.save()
            # Redirect to the chat view with the receiver of the message just sent
            return redirect('chat:chat_view', receiver_id=chat_message.receiver.id)
    # If not POST or form invalid, redirect to the generic chat view or a specific user chat if applicable
    # This part might need refinement based on how you want to handle errors or direct access.
    # For now, redirecting to the main chat page without a selected user.
    return redirect('chat:chat_view')

@login_required
def unread_messages_count(request):
    if request.user.is_authenticated:
        count = ChatMessage.objects.filter(receiver=request.user, is_read=False).count()
        return JsonResponse({'unread_count': count})
    return JsonResponse({'unread_count': 0})