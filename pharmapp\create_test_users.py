#!/usr/bin/env python
"""
Script to create test users for chat testing
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pharmapp.settings')
django.setup()

from django.contrib.auth import get_user_model
from userauth.models import Profile

User = get_user_model()

def create_test_users():
    """Create test users for chat testing"""
    print("👥 Creating Test Users for Chat Testing...")
    
    test_users = [
        {
            'username': 'alice_pharmacist',
            'mobile': '5551234567',
            'password': 'testpass123',
            'full_name': '<PERSON>',
            'user_type': 'Pharmacist'
        },
        {
            'username': 'bob_manager',
            'mobile': '5552345678',
            'password': 'testpass123',
            'full_name': '<PERSON>',
            'user_type': 'Manager'
        },
        {
            'username': 'carol_tech',
            'mobile': '5553456789',
            'password': 'testpass123',
            'full_name': '<PERSON>',
            'user_type': 'Pharm-Tech'
        },
        {
            'username': 'david_sales',
            'mobile': '5554567890',
            'password': 'testpass123',
            'full_name': 'David Wilson',
            'user_type': 'Salesperson'
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            # Check if user already exists
            if User.objects.filter(mobile=user_data['mobile']).exists():
                print(f"  ⚠️  User {user_data['username']} already exists")
                continue
            
            # Create user
            user = User.objects.create_user(
                username=user_data['username'],
                mobile=user_data['mobile'],
                password=user_data['password']
            )
            
            # Create or update profile
            profile, created = Profile.objects.get_or_create(
                user=user,
                defaults={
                    'full_name': user_data['full_name'],
                    'user_type': user_data['user_type']
                }
            )
            
            created_users.append(user)
            print(f"  ✅ Created user: {user_data['username']} ({user_data['user_type']})")
            
        except Exception as e:
            print(f"  ❌ Error creating {user_data['username']}: {e}")
    
    print(f"\n🎉 Successfully created {len(created_users)} test users!")
    
    if created_users:
        print("\n📋 Test User Credentials:")
        print("=" * 50)
        for user_data in test_users:
            if not User.objects.filter(mobile=user_data['mobile']).exists():
                continue
            print(f"Username: {user_data['username']}")
            print(f"Mobile: {user_data['mobile']}")
            print(f"Password: {user_data['password']}")
            print(f"Role: {user_data['user_type']}")
            print("-" * 30)
    
    return created_users

def show_existing_users():
    """Show existing users in the system"""
    print("\n👤 Existing Users in System:")
    print("=" * 50)
    
    users = User.objects.all().select_related('profile')
    
    if not users:
        print("No users found in the system.")
        return
    
    for user in users:
        profile = getattr(user, 'profile', None)
        user_type = profile.user_type if profile else 'No Profile'
        full_name = profile.full_name if profile else 'No Name'
        
        print(f"ID: {user.id}")
        print(f"Username: {user.username}")
        print(f"Mobile: {user.mobile}")
        print(f"Full Name: {full_name}")
        print(f"Role: {user_type}")
        print(f"Active: {user.is_active}")
        print(f"Superuser: {user.is_superuser}")
        print("-" * 30)

def main():
    """Main function"""
    print("🚀 Chat Test Users Setup")
    print("=" * 50)
    
    # Show existing users first
    show_existing_users()
    
    # Create test users
    create_test_users()
    
    print("\n💡 How to Test Chat:")
    print("1. Login with any of the test users above")
    print("2. Go to http://127.0.0.1:8000/chat/")
    print("3. Select another user to start chatting")
    print("4. Open another browser/incognito window")
    print("5. Login with a different test user")
    print("6. Start chatting between the two users!")
    
    print("\n🔗 Quick Links:")
    print("- Chat Interface: http://127.0.0.1:8000/chat/")
    print("- Admin Panel: http://127.0.0.1:8000/admin/")
    print("- Main App: http://127.0.0.1:8000/")

if __name__ == '__main__':
    main()
